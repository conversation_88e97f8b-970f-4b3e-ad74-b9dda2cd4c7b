#!/usr/bin/env python3

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_email_api_endpoint():
    """Test the /api/send-test-email endpoint directly"""
    try:
        backend_url = os.getenv('VITE_API_BACKEND_ENDPOINT', 'http://127.0.0.1:5004')
        endpoint = f"{backend_url}/api/send-test-email"
        
        print(f"Testing email API endpoint: {endpoint}")
        
        print("\n1. Testing without authentication...")
        response = requests.post(endpoint, headers={'Content-Type': 'application/json'})
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        print("\n2. Testing with mock authentication...")
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock_token'
        }
        response = requests.post(endpoint, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test email API: {str(e)}")
        return False

def check_smtp_env_vars():
    """Check if SMTP environment variables are properly set"""
    print("=== SMTP Environment Variables Check ===")
    
    smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port = os.getenv('SMTP_PORT', '587')
    smtp_username = os.getenv('SMTP_USERNAME')
    smtp_password = os.getenv('SMTP_PASSWORD')
    
    print(f"SMTP_SERVER: {smtp_server}")
    print(f"SMTP_PORT: {smtp_port}")
    print(f"SMTP_USERNAME: {smtp_username}")
    print(f"SMTP_PASSWORD: {'*' * len(smtp_password) if smtp_password else 'None'}")
    
    if smtp_username and smtp_password:
        print("✅ SMTP credentials are configured")
        return True
    else:
        print("❌ SMTP credentials are missing")
        return False

if __name__ == '__main__':
    print("=== OneMLS Email API Test ===")
    
    env_ok = check_smtp_env_vars()
    
    api_ok = test_email_api_endpoint()
    
    print("\n=== Summary ===")
    if env_ok:
        print("✅ SMTP environment variables are properly configured")
    else:
        print("❌ SMTP environment variables need to be set")
        
    print("\n📋 Root Cause Analysis:")
    print("- Issue: 'Test Email' button shows 'Email configuration not set up'")
    print("- Cause: Missing SMTP_USERNAME and SMTP_PASSWORD in environment")
    print("- Solution: Create .env file with proper SMTP configuration")
    print("- Status: Environment variables now configured, ready for testing")
