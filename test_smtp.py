#!/usr/bin/env python3

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

def test_smtp_configuration():
    """Test SMTP configuration using the same logic as the backend"""
    try:
        smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.getenv('SMTP_PORT', '587'))
        smtp_username = os.getenv('SMTP_USERNAME')
        smtp_password = os.getenv('SMTP_PASSWORD')
        
        print(f"Testing SMTP configuration:")
        print(f"Server: {smtp_server}")
        print(f"Port: {smtp_port}")
        print(f"Username: {smtp_username}")
        print(f"Password: {'*' * len(smtp_password) if smtp_password else 'None'}")
        
        if not smtp_username or not smtp_password:
            print("❌ SMTP configuration not complete - missing username or password")
            return False
        
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = smtp_username  # Send to self for testing
        msg['Subject'] = 'OneMLS SMTP Test Email'
        
        body = f"""Hello!

This is a test email to verify SMTP configuration for OneMLS.

Sent at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC

If you received this email, your SMTP configuration is working correctly.

Best regards,
OneMLS Test Script"""
        
        msg.attach(MIMEText(body, 'plain'))
        
        print("Connecting to SMTP server...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        
        print("Authenticating...")
        server.login(smtp_username, smtp_password)
        
        print("Sending test email...")
        server.send_message(msg)
        server.quit()
        
        print(f"✅ Test email sent successfully to {smtp_username}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send test email: {str(e)}")
        return False

if __name__ == '__main__':
    print("=== OneMLS SMTP Configuration Test ===")
    success = test_smtp_configuration()
    if success:
        print("\n🎉 SMTP configuration is working correctly!")
    else:
        print("\n💥 SMTP configuration needs to be fixed.")
