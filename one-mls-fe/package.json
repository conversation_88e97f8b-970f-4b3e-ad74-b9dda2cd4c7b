{"name": "one-mls-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.0.3", "mapbox-gl": "^3.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-slick": "^0.30.3", "recharts": "^3.0.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}