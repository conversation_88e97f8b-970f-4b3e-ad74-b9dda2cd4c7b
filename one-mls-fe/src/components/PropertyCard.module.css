.propertyCard {
  background: var(--white);
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s, box-shadow 0.2s;
  text-decoration: none;
  color: inherit;
  display: block;
  overflow: hidden;
  margin-bottom: 0.75rem;
  height: auto;
}

.propertyCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* Horizontal card layout */
.horizontalCard {
  display: flex;
  min-height: 325px; /* Reduced from 500px */
  height: 100%;
}

.cardContent {
  display: flex;
  width: 100%;
  height: 100%;
}

.cardImageContainer {
  position: relative;
  width: 70%;
  min-width: 280px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cardDetailsContainer {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.listingTag {
  background: #3b82f6;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.85rem;
}

.addressAndSqft {
  font-size: 1.15rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.85rem 0;
  line-height: 1.3;
  display: flex;
  align-items: center;
}

.price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 1rem;
}

.detailsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.propertyDetails {
  display: flex;
  align-items: center;
  color: #64748b;
  font-size: 0.9rem;
}

.detailDot {
  margin: 0 0.5rem;
  color: #94a3b8;
}

.timestamp {
  color: #94a3b8;
  font-size: 0.85rem;
  white-space: nowrap;
}

.cardWithSave {
  position: relative;
}

.saveButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  z-index: 2;
  backdrop-filter: blur(4px);
}

.saveButton:hover {
  background: rgba(255, 255, 255, 1);
  color: #e74c3c;
  transform: scale(1.1);
}

.saveButton.saved {
  background: #e74c3c;
  color: white;
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Carousel styles */
.carousel {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
  flex: 1;
}

.carousel::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome/Safari */
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  scroll-snap-align: start;
  position: relative;
}

.propertyImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  flex: 1;
}

/* Next button */
.nextButton {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0.9;
  padding: 0;
}

.nextButton:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.nextButton:active {
  transform: translateY(-50%) scale(0.95);
}

.nextButton svg {
  width: 18px;
  height: 18px;
}

.propertyCard:hover .propertyImage {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .cardContent {
    flex-direction: column;
  }
  
  .cardImageContainer {
    width: 100%;
    height: 220px;
  }
  
  .cardDetailsContainer {
    padding: 1.25rem;
  }
  
  .detailsRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.6rem;
  }
  
  .address {
    font-size: 1.05rem;
    margin-bottom: 0.75rem;
  }
  
  .price {
    font-size: 1.15rem;
    margin-bottom: 0.85rem;
  }
  
  .listingTag {
    margin-bottom: 0.75rem;
  }
}
