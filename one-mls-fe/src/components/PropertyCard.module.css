.propertyCard {
  background: var(--white);
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s, box-shadow 0.2s;
  text-decoration: none;
  color: inherit;
  display: block;
  overflow: hidden;
  margin-bottom: 0.75rem;
  height: auto;
}

.propertyCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* Horizontal card layout */
.horizontalCard {
  display: flex;
  min-height: 325px; /* Reduced from 500px */
  height: 100%;
}

.cardContent {
  display: flex;
  width: 100%;
  height: 100%;
}

.cardImageContainer {
  position: relative;
  width: 70%;
  min-width: 280px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cardDetailsContainer {
  width: 30%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 0.75rem;
}

.listingTag {
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 0.75rem;
  line-height: 1.2;
}

.addressAndSqft {
  font-size: 0.95rem;
  font-weight: 500;
  color: #475569;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detailsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
}

.propertyDetails {
  display: flex;
  align-items: center;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
}

.detailDot {
  margin: 0 0.5rem;
  color: #94a3b8;
}

.timestamp {
  color: #94a3b8;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
}

.cardWithSave {
  position: relative;
}

.saveButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  z-index: 2;
  backdrop-filter: blur(4px);
}

.saveButton:hover {
  background: rgba(255, 255, 255, 1);
  color: #e74c3c;
  transform: scale(1.1);
}

.saveButton.saved {
  background: #e74c3c;
  color: white;
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Carousel styles */
.carousel {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
  flex: 1;
}

.carousel::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome/Safari */
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  scroll-snap-align: start;
  position: relative;
}

.propertyImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  flex: 1;
}

/* Next button */
.nextButton {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0.9;
  padding: 0;
}

.nextButton:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.nextButton:active {
  transform: translateY(-50%) scale(0.95);
}

.nextButton svg {
  width: 18px;
  height: 18px;
}

.propertyCard:hover .propertyImage {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .cardContent {
    flex-direction: column;
  }

  .cardImageContainer {
    width: 100%;
    height: 220px;
  }

  .cardDetailsContainer {
    width: 100%;
    padding: 1.25rem;
  }

  .detailsRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.6rem;
  }

  .addressAndSqft {
    font-size: 0.9rem;
    margin-bottom: 0.6rem;
  }

  .price {
    font-size: 1.3rem;
    margin-bottom: 0.6rem;
  }

  .listingTag {
    margin-bottom: 0.6rem;
  }
}

/* Carousel Controls */
.carouselButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
  z-index: 3;
  backdrop-filter: blur(4px);
}

.carouselButton:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.prevButton {
  left: 8px;
}

.nextButton {
  right: 8px;
}

.imageIndicators {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 6px;
  z-index: 2;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

.imageCounter {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 2;
}

/* Additional Features */
.additionalFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin: 1rem 0;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f1f5f9;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #475569;
}

.featureIcon {
  font-size: 1rem;
}

.featureText {
  font-weight: 500;
}

/* Description Preview */
.descriptionPreview {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e2e8f0;
}
