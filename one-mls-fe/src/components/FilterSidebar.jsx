import { useState } from 'react';
import styles from './FilterSidebar.module.css';
import { propertyTypes, bedroomOptions, bathroomOptions } from '../data/mockProperties';

function FilterSidebar({ filters, onFiltersChange, isOpen, onToggle, isHorizontal = false }) {
  const [localFilters, setLocalFilters] = useState(filters);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePriceChange = (type, value) => {
    const newPriceRange = { ...localFilters.priceRange, [type]: parseInt(value) || 0 };
    handleFilterChange('priceRange', newPriceRange);
  };

  const handleSqftChange = (type, value) => {
    const newSqftRange = { ...localFilters.sqftRange, [type]: parseInt(value) || 0 };
    handleFilterChange('sqftRange', newSqftRange);
  };

  const handlePropertyTypeChange = (type) => {
    const newTypes = localFilters.propertyTypes.includes(type)
      ? localFilters.propertyTypes.filter(t => t !== type)
      : [...localFilters.propertyTypes, type];
    handleFilterChange('propertyTypes', newTypes);
  };

  const clearFilters = () => {
    const defaultFilters = {
      priceRange: { min: 0, max: 2000000 },
      bedrooms: '',
      bathrooms: '',
      propertyTypes: [],
      sqftRange: { min: 0, max: 5000 }
    };
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const formatPrice = (price) => {
    if (price >= 1000000) {
      return `$${(price / 1000000).toFixed(1)}M`;
    } else if (price >= 1000) {
      return `$${(price / 1000).toFixed(0)}K`;
    }
    return `$${price}`;
  };

  return (
    <>
      {isOpen && !isHorizontal && <div className={styles.overlay} onClick={onToggle} />}
      <div className={`${styles.sidebar} ${isOpen ? styles.open : ''} ${isHorizontal ? styles.horizontal : ''}`}>
        {!isHorizontal && (
          <div className={styles.sidebarHeader}>
            <h3 className={styles.title}>Filters</h3>
            <button className={styles.toggleButton} onClick={onToggle}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        )}

        <div className={`${styles.filtersContainer} ${isHorizontal ? styles.horizontalContainer : ''}`}>

        <div className={styles.filterSection}>
          <h4 className={styles.filterTitle}>Price Range</h4>
          <div className={styles.priceInputs}>
            <div className={styles.inputGroup}>
              <label>Min Price</label>
              <input
                type="number"
                value={localFilters.priceRange.min || ''}
                onChange={(e) => handlePriceChange('min', e.target.value)}
                placeholder="$0"
                className={styles.input}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Max Price</label>
              <input
                type="number"
                value={localFilters.priceRange.max || ''}
                onChange={(e) => handlePriceChange('max', e.target.value)}
                placeholder="$2,000,000"
                className={styles.input}
              />
            </div>
          </div>
          <div className={styles.priceRange}>
            <input
              type="range"
              min="0"
              max="2000000"
              step="50000"
              value={localFilters.priceRange.min}
              onChange={(e) => handlePriceChange('min', e.target.value)}
              className={styles.rangeSlider}
            />
            <input
              type="range"
              min="0"
              max="2000000"
              step="50000"
              value={localFilters.priceRange.max}
              onChange={(e) => handlePriceChange('max', e.target.value)}
              className={styles.rangeSlider}
            />
          </div>
          <div className={styles.rangeLabels}>
            <span>{formatPrice(localFilters.priceRange.min)}</span>
            <span>{formatPrice(localFilters.priceRange.max)}</span>
          </div>
        </div>

        <div className={styles.filterSection}>
          <h4 className={styles.filterTitle}>Bedrooms</h4>
          <select
            value={localFilters.bedrooms}
            onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
            className={styles.select}
          >
            <option value="">Any</option>
            {bedroomOptions.map(num => (
              <option key={num} value={num}>{num}+ bed</option>
            ))}
          </select>
        </div>

        <div className={styles.filterSection}>
          <h4 className={styles.filterTitle}>Bathrooms</h4>
          <select
            value={localFilters.bathrooms}
            onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
            className={styles.select}
          >
            <option value="">Any</option>
            {bathroomOptions.map(num => (
              <option key={num} value={num}>{num}+ bath</option>
            ))}
          </select>
        </div>

        <div className={styles.filterSection}>
          <h4 className={styles.filterTitle}>Property Type</h4>
          <div className={styles.checkboxGroup}>
            {propertyTypes.map(type => (
              <label key={type} className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={localFilters.propertyTypes.includes(type)}
                  onChange={() => handlePropertyTypeChange(type)}
                  className={styles.checkbox}
                />
                <span className={styles.checkboxText}>{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className={styles.filterSection}>
          <h4 className={styles.filterTitle}>Square Feet</h4>
          <div className={styles.priceInputs}>
            <div className={styles.inputGroup}>
              <label>Min Sqft</label>
              <input
                type="number"
                value={localFilters.sqftRange.min || ''}
                onChange={(e) => handleSqftChange('min', e.target.value)}
                placeholder="0"
                className={styles.input}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Max Sqft</label>
              <input
                type="number"
                value={localFilters.sqftRange.max || ''}
                onChange={(e) => handleSqftChange('max', e.target.value)}
                placeholder="5,000"
                className={styles.input}
              />
            </div>
          </div>
        </div>

          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              Clear All Filters
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default FilterSidebar;
