import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { saveProperty, unsaveProperty } from '../utils/api';
import { isAuthenticated } from '../utils/auth';
import styles from './PropertyCard.module.css';

function PropertyCardWithSave({ property, initialSaved = false, onSaveChange }) {
  const [isSaved, setIsSaved] = useState(initialSaved);
  const [isLoading, setIsLoading] = useState(false);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const handleSaveToggle = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isAuthenticated()) {
      alert('Please log in to save properties');
      return;
    }

    setIsLoading(true);
    try {
      if (isSaved) {
        await unsaveProperty(property.id);
        setIsSaved(false);
        onSaveChange?.(property.id, false);
        alert('Listing removed from saved!');
      } else {
        await saveProperty(property.id);
        setIsSaved(true);
        onSaveChange?.(property.id, true);
        alert('Listing saved!');
      }
    } catch (error) {
      console.error('Error toggling save status:', error);
      alert(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Determine listing type tag
  const getListingTag = () => {
    return 'On Sale';
  };

  // Format timestamp
  const getTimestamp = () => {
    if (property.listedDate) {
      const listedDate = new Date(property.listedDate);
      const now = new Date();
      const diffTime = Math.abs(now - listedDate);
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      
      if (diffDays > 0) {
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
      } else {
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      }
    }
    return 'Just listed';
  };

  // Ensure we have an array of images, defaulting to the single image if needed
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const carouselRef = useRef(null);
  
  const images = property.images && property.images.length > 0 
    ? property.images.slice(0, 5) // Limit to max 5 images
    : property.imageUrl 
      ? [property.imageUrl]
      : ['/placeholder-property.jpg'];
      
  // Make sure we always have an array
  const imageArray = Array.isArray(images) ? images : [];

  const showNextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    
    setCurrentImageIndex((prevIndex) => (prevIndex + 1) % imageArray.length);
  };

  useEffect(() => {
    if (carouselRef.current) {
      carouselRef.current.scrollTo({
        left: carouselRef.current.offsetWidth * currentImageIndex,
        behavior: 'smooth'
      });
    }
  }, [currentImageIndex]);

  return (
    <Link to={`/property/${property.id}`} className={`${styles.propertyCard} ${styles.horizontalCard}`}>
      <div className={styles.cardContent}>
        <div className={styles.cardImageContainer}>
          <div className={styles.carousel} ref={carouselRef} onClick={(e) => e.stopPropagation()}>
            {imageArray.length > 0 ? (
              imageArray.map((img, index) => (
                <div key={index} className={styles.slide}>
                  <img 
                    src={img} 
                    alt={`${property.address} - ${index + 1}`}
                    className={styles.propertyImage}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = '/placeholder-property.jpg';
                    }}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              ))
            ) : (
              <div className={styles.slide}>
                <img 
                  src="/placeholder-property.jpg" 
                  alt="No images available"
                  className={styles.propertyImage}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            )}
          </div>
          
          {images.length > 1 && (
            <button 
              onClick={showNextImage}
              className={styles.nextButton}
              aria-label="Next image"
            >
              <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
              </svg>
            </button>
          )}
          
          {isAuthenticated() && (
            <button 
              onClick={handleSaveToggle}
              disabled={isLoading}
              className={`${styles.saveButton} ${isSaved ? styles.saved : ''}`}
              title={isSaved ? 'Remove from saved' : 'Save property'}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
            </button>
          )}
        </div>
        
        <div className={styles.cardDetailsContainer}>
          <div className={styles.listingTag}>
            {getListingTag()}
          </div>
          
          <div className={styles.price}>
            {typeof property.price === 'number' ? formatPrice(property.price) : property.price}
          </div>
          
          <div className={styles.addressAndSqft}>
            <span>{property.address}</span>
            <span className={styles.detailDot}>•</span>
            <span>{property.squareFootage.toLocaleString()} sq ft</span>
          </div>
          
          <div className={styles.detailsRow}>
            <div className={styles.propertyDetails}>
              <span>{property.bedrooms} bed</span>
              <span className={styles.detailDot}>•</span>
              <span>{property.bathrooms} bath</span>
            </div>
            
            <div className={styles.timestamp}>
              {getTimestamp()}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}



export default PropertyCardWithSave;
