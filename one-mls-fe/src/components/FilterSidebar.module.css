.sidebar {
  position: fixed;
  top: 0;
  left: -380px;
  width: 380px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 2px 0 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow-y: auto;
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar.open {
  left: 0;
}

/* Horizontal Filter Bar */
.horizontalFilters {
  width: 100%;
  background: white;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.filterBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1.5rem 2rem;
  flex-wrap: wrap;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

.filterItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 120px;
  flex: 1;
}

.filterLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priceRange {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priceInput {
  width: 80px;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.priceInput:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.priceSeparator {
  color: #6b7280;
  font-weight: 500;
}

.filterSelect {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  min-width: 120px;
}

.filterSelect:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.propertyTypeButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.propertyTypeButton {
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.75rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.propertyTypeButton:hover {
  border-color: #2563eb;
  background: #f3f4f6;
}

.propertyTypeButton.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.clearFiltersButton {
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  flex-shrink: 0;
}

.clearFiltersButton:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggleButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.toggleButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.filterSection {
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.15);
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
  margin: 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filterSection:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}



.filterTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterTitle::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.priceInputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.inputGroup label {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
}

.input {
  padding: 1rem;
  border: 2px solid rgba(229, 231, 235, 0.3);
  border-radius: 12px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  background: white;
}

.input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.priceRange {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.rangeSlider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--gray-200);
  outline: none;
  -webkit-appearance: none;
}

.rangeSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
}

.rangeSlider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

.rangeLabels {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--gray-600);
}

.select {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(229, 231, 235, 0.3);
  border-radius: 12px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  backdrop-filter: blur(10px);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;
}

.select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  background: white;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--text-color);
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(229, 231, 235, 0.3);
  font-weight: 500;
}

.checkboxLabel:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox:checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  transform: scale(1.05);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 1px;
  left: 3px;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.checkboxText {
  user-select: none;
}

.filterActions {
  padding: 2rem 2.5rem;
  margin: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}



.clearButton {
  width: 100%;
  padding: 1.25rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}



.clearButton:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.clearButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

@media (min-width: 1024px) {
  .sidebar {
    position: relative;
    left: 0;
    width: clamp(320px, 25vw, 400px);
    min-width: 320px;
    max-width: 400px;
    height: auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(229, 231, 235, 0.3);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 0 20px 20px 0;
  }

  .overlay {
    display: none;
  }

  .toggleButton {
    display: none;
  }

  .sidebarHeader {
    position: static;
    border-radius: 0 20px 0 0;
  }
}

@media (max-width: 1024px) {
  .filterBar {
    gap: 0.75rem;
    padding: 1rem 2rem;
    width: 100%;
    justify-content: space-between;
  }

  .filterItem {
    min-width: 100px;
    flex: 1;
  }

  .priceInput {
    width: 70px;
  }

  .filterSelect {
    min-width: 100px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 320px;
  }

  .priceInputs {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .filterSection {
    padding: 1.5rem 2rem;
  }

  .sidebarHeader {
    padding: 1.5rem 2rem;
  }

  .filterActions {
    padding: 1.5rem 2rem;
  }

  .filterBar {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem 2rem;
    width: 100%;
  }

  .filterItem {
    min-width: auto;
  }

  .priceRange {
    justify-content: center;
  }

  .propertyTypeButtons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 300px;
  }

  .filterSection {
    padding: 1.25rem 1.5rem;
    margin: 0.25rem;
  }

  .sidebarHeader {
    padding: 1.25rem 1.5rem;
  }

  .filterActions {
    padding: 1.25rem 1.5rem;
    margin: 0.25rem;
  }

  .input, .select {
    padding: 0.875rem;
    font-size: 0.9rem;
  }

  .checkboxLabel {
    padding: 0.625rem;
    font-size: 0.9rem;
  }

  .clearButton {
    padding: 1rem;
    font-size: 0.9rem;
  }
}
