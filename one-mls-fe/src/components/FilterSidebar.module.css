.sidebar {
  position: fixed;
  top: 0;
  left: -380px;
  width: 380px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 2px 0 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow-y: auto;
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar.open {
  left: 0;
}

/* Horizontal Filter Bar */
.horizontalFilters {
  width: 100%;
  background: white;
  border: none;
  border-radius: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 0.75rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
  flex-wrap: nowrap;
  white-space: nowrap;
  overflow-x: auto;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 5;
}

.filterBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 0.6rem 2rem;
  flex-wrap: nowrap;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  max-width: 1400px;
  margin: 0 auto;
}

.filterItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
  flex: 1;
}

.filterLabel {
  font-size: 0.7rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.searchContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  z-index: 1;
  pointer-events: none;
}

.searchInput {
  width: 100%;
  padding: 0.4rem 0.75rem 0.4rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.8rem;
  background: white;
  transition: all 0.2s ease;
  min-width: 180px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.searchInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.searchInput::placeholder {
  color: #9ca3af;
}

.priceRange {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.priceInput {
  width: 60px;
  padding: 0.4rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.8rem;
  background: white;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.priceInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.priceSeparator {
  color: #6b7280;
  font-weight: 500;
  margin: 0 0.1rem;
}

.filterSelect {
  padding: 0.4rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.8rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  min-width: 80px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.filterSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.clearFiltersButton {
  padding: 0.4rem 0.75rem;
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3);
  margin-top: 1.35rem;
}

.clearFiltersButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.4);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggleButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.toggleButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.filterSection {
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.15);
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
  margin: 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filterSection:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.filterTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterTitle::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.priceInputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.inputGroup label {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
}

.input {
  padding: 1rem;
  border: 2px solid rgba(229, 231, 235, 0.3);
  border-radius: 12px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  background: white;
}

.input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.priceRange {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.rangeSlider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--gray-200);
  outline: none;
  -webkit-appearance: none;
}

.rangeSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
}

.rangeSlider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

.rangeLabels {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--gray-600);
}

.select {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(229, 231, 235, 0.3);
  border-radius: 12px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  backdrop-filter: blur(10px);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;
}

.select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  background: white;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--text-color);
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(229, 231, 235, 0.3);
  font-weight: 500;
}

.checkboxLabel:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox:checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  transform: scale(1.05);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 1px;
  left: 3px;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.checkboxText {
  user-select: none;
}

.filterActions {
  padding: 2rem 2.5rem;
  margin: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.clearButton {
  width: 100%;
  padding: 1.25rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clearButton:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.clearButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
  flex: 1;
  min-width: 120px;
}

.inputGroup {
  display: flex;
  align-items: center;
}

@media (min-width: 1024px) {
  .sidebar {
    position: relative;
    left: 0;
    width: clamp(320px, 25vw, 400px);
    min-width: 320px;
    max-width: 400px;
    height: auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(229, 231, 235, 0.3);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 0 20px 20px 0;
  }

  .overlay {
    display: none;
  }

  .toggleButton {
    display: none;
  }

  .sidebarHeader {
    position: static;
    border-radius: 0 20px 0 0;
  }
}

@media (max-width: 1024px) {
  .filterBar {
    gap: 0.5rem;
    padding: 0.6rem 1rem;
  }

  .filterItem {
    min-width: 0;
  }

  .priceInput {
    width: 55px;
  }

  .filterSelect {
    min-width: 70px;
  }

  .searchInput {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .filterBar {
    padding: 0.6rem 0.75rem;
    gap: 0.4rem;
  }

  .filterItem {
    min-width: 0;
  }

  .searchInput {
    min-width: 120px;
  }

  .horizontalFilters {
    padding: 0.75rem 0.5rem;
    gap: 0.75rem;
  }
  
  .filterGroup {
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .filterBar {
    padding: 0.6rem 0.5rem;
    gap: 0.3rem;
  }

  .filterItem {
    min-width: 0;
  }
}
