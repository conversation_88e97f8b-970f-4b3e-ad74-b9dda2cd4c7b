import { Link } from 'react-router-dom';
import styles from './PropertyCard.module.css';

function PropertyCard({ property }) {
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <Link to={`/property/${property.id}`} className={styles.propertyCard}>
      <div className={styles.imageContainer}>
        <img 
          src={property.imageUrl} 
          alt={property.address}
          className={styles.propertyImage}
        />
        <div className={styles.priceTag}>
          {typeof property.price === 'number' ? formatPrice(property.price) : property.price}
        </div>
      </div>
      
      <div className={styles.propertyInfo}>
        <div className={styles.propertyHeader}>
          <h3 className={styles.address}>{property.address}</h3>
          <div className={styles.priceDisplay}>
            {typeof property.price === 'number' ? formatPrice(property.price) : property.price}
          </div>
        </div>

        <div className={styles.propertyDetails}>
          <div className={styles.detail}>
            <span className={styles.detailIcon}>🛏️</span>
            <span className={styles.detailValue}>{property.bedrooms}</span>
            <span className={styles.detailLabel}>beds</span>
          </div>
          <div className={styles.detail}>
            <span className={styles.detailIcon}>🛁</span>
            <span className={styles.detailValue}>{property.bathrooms}</span>
            <span className={styles.detailLabel}>baths</span>
          </div>
          <div className={styles.detail}>
            <span className={styles.detailIcon}>📐</span>
            <span className={styles.detailValue}>{property.squareFootage.toLocaleString()}</span>
            <span className={styles.detailLabel}>sq ft</span>
          </div>
        </div>

        <div className={styles.propertyMeta}>
          <div className={styles.propertyType}>
            {property.propertyType}
          </div>
          <div className={styles.yearBuilt}>
            Built in {property.yearBuilt}
          </div>
        </div>
      </div>
    </Link>
  );
}

export default PropertyCard;
