/* Chat Widget Container */
.chatWidget {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1001;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Chat <PERSON> (when closed) */
.chatButton {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  font-size: 24px;
}

.chatButton:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.chatButton:active {
  transform: scale(0.95);
}

/* Chat Window (when open) */
.assistant {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  transform: scale(0) translateY(20px);
  transform-origin: bottom right;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.assistant.open {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* Remove overlay for Intercom-style design */
.overlay {
  display: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
  position: relative;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.assistantIcon {
  font-size: 1.25rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.headerText h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.headerText span {
  font-size: 0.8rem;
  opacity: 0.9;
  font-weight: 400;
}

.toggleButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
}

.toggleButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: #f8fafc;
}

.messagesContainer::-webkit-scrollbar {
  width: 4px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: transparent;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.message {
  display: flex;
  margin-bottom: 1rem;
}

.userMessage {
  justify-content: flex-end;
}

.botMessage {
  justify-content: flex-start;
}

.messageContent {
  max-width: 85%;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.userMessage .messageContent {
  align-items: flex-end;
}

.botMessage .messageContent {
  align-items: flex-start;
}

.messageText {
  padding: 0.75rem 1rem;
  border-radius: 18px;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.userMessage .messageText {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 6px;
}

.botMessage .messageText {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 6px;
}

.bulletPoint {
  margin-left: 1rem;
  position: relative;
}

.bulletPoint::before {
  content: '•';
  position: absolute;
  left: -1rem;
  color: var(--primary-color);
  font-weight: bold;
}

.messageTime {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 0.25rem;
}

.typingIndicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.875rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
}

.typingIndicator span {
  width: 6px;
  height: 6px;
  background: var(--gray-400);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typingIndicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typingIndicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.inputForm {
  padding: 1.25rem;
  border-top: 1px solid rgba(229, 231, 235, 0.2);
  background: white;
  border-radius: 0 0 16px 16px;
}

.inputContainer {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
  background: #f8fafc;
  padding: 0.5rem;
  border-radius: 24px;
  border: 1px solid #e5e7eb;
}

.messageInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  background: transparent;
  color: #374151;
  transition: all 0.2s ease;
  resize: none;
  min-height: 20px;
  max-height: 100px;
  font-family: inherit;
}

.messageInput:focus {
  outline: none;
}

.messageInput::placeholder {
  color: #9ca3af;
}

.sendButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

/* Desktop - keep Intercom style */
@media (min-width: 1024px) {
  .assistant {
    width: 400px;
    height: 650px;
  }

  .chatButton {
    width: 64px;
    height: 64px;
    font-size: 26px;
  }
}

/* Mobile - full screen overlay */
@media (max-width: 768px) {
  .chatWidget {
    bottom: 20px;
    right: 20px;
  }

  .chatButton {
    width: 56px;
    height: 56px;
    font-size: 22px;
  }

  .assistant {
    bottom: 20px;
    right: 20px;
    width: calc(100vw - 40px);
    height: calc(100vh - 120px);
    max-width: 400px;
    max-height: 600px;
  }

  .messageContent {
    max-width: 90%;
  }

  .header {
    padding: 1rem 1.25rem;
  }

  .messagesContainer {
    padding: 1rem;
  }

  .inputForm {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .chatWidget {
    bottom: 16px;
    right: 16px;
  }

  .chatButton {
    width: 52px;
    height: 52px;
    font-size: 20px;
  }

  .assistant {
    bottom: 16px;
    right: 16px;
    width: calc(100vw - 32px);
    height: calc(100vh - 100px);
    border-radius: 12px;
  }

  .header {
    padding: 0.875rem 1rem;
    border-radius: 12px 12px 0 0;
  }

  .headerText h3 {
    font-size: 0.9rem;
  }

  .headerText span {
    font-size: 0.75rem;
  }

  .messagesContainer {
    padding: 0.875rem;
  }

  .messageText {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
  }

  .inputForm {
    padding: 0.875rem;
    border-radius: 0 0 12px 12px;
  }

  .inputContainer {
    padding: 0.375rem;
  }

  .messageInput {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
  }

  .sendButton {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .messageText {
    font-size: 0.8rem;
    padding: 0.75rem;
  }
  
  .headerText h3 {
    font-size: 1rem;
  }
  
  .headerText span {
    font-size: 0.8rem;
  }
}
