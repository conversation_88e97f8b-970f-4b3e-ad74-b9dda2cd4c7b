import { useState, useEffect, useRef } from 'react';
import styles from './RealEstateAssistant.module.css';

function RealEstateAssistant({ isOpen, onToggle, properties = [] }) {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi! I'm your real estate assistant. I can help you with property comparisons, market analysis, and rental potential. What would you like to know?",
      isBot: true,
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState('');
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateResponse = (userMessage) => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('compare') && message.includes('listing')) {
      if (properties.length >= 2) {
        const prop1 = properties[0];
        const prop2 = properties[1];
        return `I can help you compare these properties:

**${prop1.address}** - $${prop1.price.toLocaleString()}
• ${prop1.bedrooms} bed, ${prop1.bathrooms} bath
• ${prop1.squareFootage.toLocaleString()} sq ft
• Built in ${prop1.yearBuilt}

**${prop2.address}** - $${prop2.price.toLocaleString()}
• ${prop2.bedrooms} bed, ${prop2.bathrooms} bath  
• ${prop2.squareFootage.toLocaleString()} sq ft
• Built in ${prop2.yearBuilt}

The first property offers ${prop1.price < prop2.price ? 'better value' : 'more premium features'}, while the second is ${prop2.yearBuilt > prop1.yearBuilt ? 'newer construction' : 'more established'}. Would you like me to analyze specific aspects like location, amenities, or investment potential?`;
      }
      return "I'd be happy to help you compare properties! I can see the current listings and provide detailed comparisons of price, features, location benefits, and investment potential. Which specific properties would you like me to compare?";
    }

    if (message.includes('san antonio') || message.includes('phoenix')) {
      return `Great question about San Antonio vs Phoenix! Here's my analysis:

**San Antonio Advantages:**
• Lower cost of living and property prices
• Strong job market in healthcare, military, and tech
• Rich cultural heritage and growing food scene
• No state income tax in Texas

**Phoenix Advantages:**  
• Rapid population and job growth
• Better year-round weather for many
• Strong tech industry presence
• More diverse economy

**Investment Perspective:**
Phoenix has seen higher appreciation rates recently, but San Antonio offers better cash flow opportunities for rental properties. Consider your timeline, budget, and lifestyle preferences. Would you like me to dive deeper into any specific aspect?`;
    }

    if (message.includes('rental') && message.includes('potential')) {
      if (properties.length > 0) {
        const property = properties[0];
        const estimatedRent = Math.round(property.price * 0.005);
        return `For the property at ${property.address}, here's the rental analysis:

**Estimated Monthly Rent:** $${estimatedRent.toLocaleString()}
**Purchase Price:** $${property.price.toLocaleString()}
**Potential ROI:** ${((estimatedRent * 12 / property.price) * 100).toFixed(1)}%

**Factors Supporting Rental Demand:**
• ${property.bedrooms} bedrooms appeal to ${property.bedrooms >= 3 ? 'families' : 'young professionals'}
• ${property.squareFootage.toLocaleString()} sq ft provides good space
• Built in ${property.yearBuilt} - ${property.yearBuilt > 2010 ? 'modern amenities' : 'established neighborhood'}

**Considerations:**
• Property taxes: ~$${Math.round(property.propertyTaxes/12)}/month
• Maintenance costs for ${property.yearBuilt > 2015 ? 'newer' : 'older'} property
• Local rental market conditions

Would you like me to analyze the specific neighborhood rental trends or compare with other investment options?`;
      }
      return "I can help analyze rental potential! I'll need to know which specific property you're considering. I can evaluate factors like location, property type, local rental rates, and potential ROI. Which property interests you?";
    }

    if (message.includes('buy') || message.includes('invest')) {
      return `I'd be happy to help with your real estate decision! Here are key factors to consider:

**Market Timing:**
• Interest rates and local market conditions
• Seasonal trends in your target area
• Your personal financial readiness

**Property Evaluation:**
• Location and neighborhood growth potential
• Property condition and needed repairs
• Comparable sales and pricing trends

**Financial Planning:**
• Down payment and closing costs
• Monthly payment vs. current housing costs
• Emergency fund for maintenance

What specific aspect would you like to explore further? I can provide more detailed analysis based on the current listings or your particular situation.`;
    }

    return `I'm here to help with your real estate questions! I can assist with:

• **Property Comparisons** - Compare features, prices, and value
• **Market Analysis** - Local trends and investment insights  
• **Rental Potential** - ROI calculations and rental demand
• **Buying Advice** - Timing, financing, and strategy

What specific real estate topic would you like to discuss?`;
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentMessage = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      const botMessageId = Date.now() + 1;
      const initialBotMessage = {
        id: botMessageId,
        text: '',
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, initialBotMessage]);
      setIsTyping(false);

      const response = await fetch('http://localhost:5004/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentMessage,
          properties: properties,
          conversationId: conversationId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response from server');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedText = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.error) {
                throw new Error(data.error);
              }
              
              if (data.conversationId && !conversationId) {
                setConversationId(data.conversationId);
              }
              
              if (data.content) {
                accumulatedText += data.content;
                
                setMessages(prev => prev.map(msg => 
                  msg.id === botMessageId 
                    ? { ...msg, text: accumulatedText }
                    : msg
                ));
              }
              
              if (data.done) {
                return; // Streaming complete
              }
            } catch (parseError) {
              console.error('Error parsing streaming data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error in streaming chat:', error);
      setIsTyping(false);
      
      const botResponse = {
        id: Date.now() + 1,
        text: generateResponse(currentMessage),
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    }
  };

  const formatTextWithBold = (text) => {
    if (!text || typeof text !== 'string') return text;
    
    const parts = [];
    let currentIndex = 0;
    let match;
    const boldRegex = /\*\*(.*?)\*\*/g;
    
    while ((match = boldRegex.exec(text)) !== null) {
      if (match.index > currentIndex) {
        parts.push(text.slice(currentIndex, match.index));
      }
      parts.push(<strong key={`bold-${match.index}`}>{match[1]}</strong>);
      currentIndex = match.index + match[0].length;
    }
    
    if (currentIndex < text.length) {
      parts.push(text.slice(currentIndex));
    }
    
    return parts.length > 0 ? parts : text;
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={styles.chatWidget}>
      {!isOpen && (
        <button className={styles.chatButton} onClick={onToggle}>
          🏠
        </button>
      )}

      {isOpen && (
        <div className={`${styles.assistant} ${isOpen ? styles.open : ''}`}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <div className={styles.assistantIcon}>🏠</div>
              <div className={styles.headerText}>
                <h3>Real Estate Assistant</h3>
                <span>Ask me anything about properties</span>
              </div>
            </div>
            <button className={styles.toggleButton} onClick={onToggle}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>

        <div className={styles.messagesContainer}>
          {messages.map((message) => (
            <div key={message.id} className={`${styles.message} ${message.isBot ? styles.botMessage : styles.userMessage}`}>
              <div className={styles.messageContent}>
                <div className={styles.messageText}>
                  {message.text.split('\n').map((line, index) => (
                    <div key={index}>
                      {line.startsWith('• ') ? (
                        <div className={styles.bulletPoint}>{formatTextWithBold(line)}</div>
                      ) : (
                        formatTextWithBold(line)
                      )}
                    </div>
                  ))}
                </div>
                <div className={styles.messageTime}>
                  {formatTime(message.timestamp)}
                </div>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className={`${styles.message} ${styles.botMessage}`}>
              <div className={styles.messageContent}>
                <div className={styles.typingIndicator}>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

          <form onSubmit={handleSendMessage} className={styles.inputForm}>
            <div className={styles.inputContainer}>
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Type your message..."
                className={styles.messageInput}
                disabled={isTyping}
              />
              <button
                type="submit"
                disabled={!inputMessage.trim() || isTyping}
                className={styles.sendButton}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}

export default RealEstateAssistant;
