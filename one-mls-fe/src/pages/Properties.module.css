:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --blur-lg: blur(20px);
  --blur-md: blur(10px);
  --radius-lg: 20px;
  --radius-md: 16px;
  --radius-sm: 12px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease;
}

.propertiesPage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  position: relative;
}

.container {
  flex: 1;
  padding: clamp(0.5rem, 2vw, 1rem);
  display: flex;
  flex-direction: column;
  position: relative;
  max-width: 100%;
  margin: 0;
  height: 100vh;
}

.mapInfo {
  margin-bottom: 2rem;
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.mapInfo h2 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.mapInfo div {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.toolbarLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toolbarRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filterToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.filterToggle:hover {
  background: linear-gradient(135deg, #1d4ed8, #2563eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.mapStyleSelector {
  display: flex;
  align-items: center;
}

.mapStyleSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.mapStyleSelect:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}



.resultsCount {
  color: #475569;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  border-radius: 6px;
}

.mapAndListings {
  flex: 1;
  display: flex;
  gap: 1.5rem;
  min-height: 0;
}

.mapSection {
  flex: 1;
  position: relative;
  min-height: 500px;
}

.contentWrapper {
  display: flex;
  gap: 1.5rem;
  flex: 1;
  min-height: calc(100vh - 300px);
}

.map {
  flex: 3;
  height: calc(100vh - 280px);
  min-height: 600px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg), 0 0 0 1px var(--glass-border);
  border: 2px solid var(--glass-border);
  position: relative;
  min-width: 300px;
  backdrop-filter: var(--blur-md);
}

.propertiesSection {
  flex: 2;
  min-width: 280px;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg), 0 0 0 1px var(--glass-border);
  border: 2px solid var(--glass-border);
  backdrop-filter: var(--blur-lg);
}

.propertiesTitle {
  margin: 0 0 2rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

.propertiesGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow-y: auto;
  flex: 1;
  padding-right: 10px;
}

.propertiesGrid::-webkit-scrollbar {
  width: 6px;
}

.propertiesGrid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (min-width: 769px) and (max-width: 1023px) {
  .contentWrapper {
    gap: 1rem;
  }
  
  .map {
    flex: 2.5;
    min-width: 250px;
    height: calc(100vh - 300px);
    min-height: 500px;
  }
  
  .propertiesSection {
    flex: 1.5;
    min-width: 250px;
    max-width: 350px;
    padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .container {
    padding: 1rem;
  }
  
  .mapInfo {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .mapInfo h2 {
    font-size: 2rem;
  }
  
  .toolbar {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .mapAndListings {
    flex-direction: column;
    gap: 1rem;
  }
  
  .contentWrapper {
    flex-direction: column;
    gap: 1rem;
    min-height: auto;
  }
  
  .map {
    height: 50vh;
    min-height: 400px;
    order: 1;
    flex: none;
  }
  
  .propertiesSection {
    flex: none;
    min-width: auto;
    max-width: none;
    padding: 1.5rem;
    order: 2;
  }
  
  .propertiesGrid {
    max-height: 60vh;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .map {
    height: 40vh;
    min-height: 350px;
  }
  
  .propertiesSection {
    padding: 1rem;
  }
}

.loadingMessage,
.errorMessage,
.noPropertiesMessage {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-weight: 500;
  font-size: 1.125rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #cbd5e1;
  margin: 1rem 0;
}

.loadingMessage {
  background: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

.errorMessage {
  color: #dc2626;
  background: #fef2f2;
  border-color: #fecaca;
}

.noPropertiesMessage {
  color: #64748b;
  background: #f8fafc;
  border-color: #cbd5e1;
}
