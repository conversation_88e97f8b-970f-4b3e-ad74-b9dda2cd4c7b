:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --blur-lg: blur(20px);
  --blur-md: blur(10px);
  --radius-lg: 20px;
  --radius-md: 16px;
  --radius-sm: 12px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease;
}

.propertiesPage {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  position: relative;
  overflow: hidden;
}

.container {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  max-width: 100%;
  margin: 0;
  height: 100vh;
  background: #f8fafc;
}

/* Page Header */
.pageHeader {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem 2rem 1.5rem 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pageTitle {
  margin: 0;
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.headerMeta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.resultsCount {
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
}

.locationStatus {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Header Overlay */
.headerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

/* Filters Overlay */
.filtersOverlay {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow-x: auto;
  backdrop-filter: blur(10px);
}

.mapStyleSelect {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.mapStyleSelect:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* New layout styles */
.contentContainer {
  display: flex;
  flex: 1;
  height: 100vh;
  overflow: hidden;
}

.propertiesColumn {
  width: 30%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e2e8f0;
  overflow: hidden;
}

.mapColumn {
  width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  flex: 1;
}

.propertiesTitle {
  margin: 0;
  padding: 1rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.mapControls {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 10;
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map {
  width: 100%;
  height: 100%;
  flex: 1;
}

.propertiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  overflow-y: auto;
  flex: 1;
  padding: 1rem;
  align-content: start;
}

.propertiesGrid::-webkit-scrollbar {
  width: 6px;
}

.propertiesGrid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentContainer {
    flex-direction: column;
    height: 100vh;
    padding-top: 120px; /* Account for header + filters overlay */
  }

  .propertiesColumn {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .propertiesGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }

  .mapColumn {
    width: 100%;
    height: 60vh;
    flex: 1;
  }
}

@media (max-width: 768px) {
  .contentContainer {
    padding-top: 140px; /* More space for mobile header + filters */
  }

  .propertiesColumn {
    max-height: 35vh;
  }

  .propertiesGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .mapColumn {
    height: 65vh;
    flex: 1;
  }

  .mapControls {
    top: 10px;
    right: 10px;
  }

  .headerOverlay {
    background-color: rgba(255, 255, 255, 0.98);
  }

  .filtersOverlay {
    background: rgba(255, 255, 255, 0.98);
  }
}

.loadingMessage,
.errorMessage,
.noPropertiesMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
  height: 100%;
}

.loadingMessage {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

.errorMessage {
  color: #ef4444;
  background: rgba(254, 226, 226, 0.5);
  backdrop-filter: blur(4px);
}

.noPropertiesMessage {
  background: rgba(241, 245, 249, 0.8);
  backdrop-filter: blur(4px);
}



