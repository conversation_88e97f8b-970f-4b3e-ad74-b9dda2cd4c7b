:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --blur-lg: blur(20px);
  --blur-md: blur(10px);
  --radius-lg: 20px;
  --radius-md: 16px;
  --radius-sm: 12px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease;
}

.propertiesPage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  position: relative;
}

.container {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  max-width: 100%;
  margin: 0;
  height: 100vh;
  background: #f8fafc;
}

/* Page Header */
.pageHeader {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem 2rem 1.5rem 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pageTitle {
  margin: 0;
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.headerMeta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.resultsCount {
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
}

.locationStatus {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Filters Section */
.filtersSection {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 1.5rem 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.mapStyleSelect {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.mapStyleSelect:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Main Content Layout */
.mainContent {
  flex: 1;
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  min-height: 0;
}

.propertiesSection {
  flex: 1;
  min-width: 400px;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.propertiesSectionHeader {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.propertiesTitle {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.mapSection {
  flex: 2;
  position: relative;
  min-width: 500px;
  display: flex;
  flex-direction: column;
}

.mapHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-bottom: none;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mapTitle {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.map {
  flex: 1;
  width: 100%;
  height: calc(100vh - 320px);
  min-height: 600px;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  border-top: none;
}

.propertiesGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  flex: 1;
  padding: 1rem 1.5rem 1.5rem 1.5rem;
}

.propertiesGrid::-webkit-scrollbar {
  width: 6px;
}

.propertiesGrid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.propertiesGrid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .mainContent {
    padding: 1rem;
    gap: 1rem;
  }

  .propertiesSection {
    min-width: 350px;
  }

  .mapSection {
    min-width: 400px;
  }
}

@media (max-width: 1024px) {
  .pageHeader {
    padding: 1.5rem 1rem;
  }

  .headerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .pageTitle {
    font-size: 2rem;
  }

  .filtersSection {
    padding: 1rem;
  }

  .mainContent {
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
  }

  .propertiesSection {
    min-width: auto;
    max-width: none;
    order: 2;
  }

  .mapSection {
    min-width: auto;
    order: 1;
  }

  .mapHeader {
    padding: 0.75rem 1rem;
  }

  .mapTitle {
    font-size: 1.125rem;
  }

  .map {
    height: 50vh;
    min-height: 400px;
  }

  .propertiesGrid {
    max-height: 60vh;
  }
}

@media (max-width: 768px) {
  .pageHeader {
    padding: 1rem;
  }

  .pageTitle {
    font-size: 1.75rem;
  }

  .headerMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .mapHeader {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .mapTitle {
    font-size: 1rem;
  }

  .map {
    height: 40vh;
    min-height: 350px;
  }

  .propertiesSection {
    padding: 0;
  }

  .propertiesSectionHeader {
    padding: 1rem;
  }

  .propertiesGrid {
    padding: 1rem;
  }
}

.loadingMessage,
.errorMessage,
.noPropertiesMessage {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-weight: 500;
  font-size: 1.125rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #cbd5e1;
  margin: 1rem 0;
}

.loadingMessage {
  background: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

.errorMessage {
  color: #dc2626;
  background: #fef2f2;
  border-color: #fecaca;
}

.noPropertiesMessage {
  color: #64748b;
  background: #f8fafc;
  border-color: #cbd5e1;
}
