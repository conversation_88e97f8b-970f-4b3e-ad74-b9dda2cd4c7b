import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import mapboxgl from 'mapbox-gl';
import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder';
import Header from '../components/Header';
import PropertyCard from '../components/PropertyCard';
import PropertyCardWithSave from '../components/PropertyCardWithSave';
import FilterSidebar from '../components/FilterSidebar';
import RealEstateAssistant from '../components/RealEstateAssistant';
import { fetchProperties, fetchPropertiesWithFilters } from '../utils/api';
import { isAuthenticated } from '../utils/auth';
import { filterPropertiesByRadius } from '../utils/distance';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css';
import styles from './Properties.module.css';

// You'll need to get a Mapbox access token from https://account.mapbox.com/
// and replace this placeholder with your actual token
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;
console.log("mapbox access token", import.meta.env.VITE_MAPBOX_ACCESS_TOKEN);

if (!mapboxgl.accessToken) {
  console.error('Mapbox access token is not set. Please check your .env file.');
}

function Properties() {
  const [searchParams] = useSearchParams();
  const searchQuery = searchParams.get('search');
  
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [lng, setLng] = useState(-121.9885); // Default: Fremont, CA longitude
  const [lat, setLat] = useState(37.5485);   // Default: Fremont, CA latitude
  const [zoom, setZoom] = useState(12);
  const [isGettingLocation, setIsGettingLocation] = useState(true);
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchCenter, setSearchCenter] = useState(null); // Store search location center
  const [filters, setFilters] = useState({
    priceRange: { min: 0, max: 2000000 },
    bedrooms: '',
    bathrooms: '',
    propertyTypes: [],
    sqftRange: { min: 0, max: 5000 }
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isAssistantOpen, setIsAssistantOpen] = useState(false);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    if (searchQuery && mapboxgl.accessToken) {
      const geocodeSearch = async () => {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchQuery)}.json?access_token=${mapboxgl.accessToken}&country=US&limit=1`
          );
          const data = await response.json();
          
          if (data.features && data.features.length > 0) {
            const [longitude, latitude] = data.features[0].center;
            setLng(longitude);
            setLat(latitude);
            setSearchCenter({ lat: latitude, lng: longitude });
            setIsGettingLocation(false);
            
            // Update map center if map is already initialized
            if (map.current) {
              map.current.setCenter([longitude, latitude]);
              map.current.setZoom(12);
            }
          } else {
            console.warn('No results found for search query:', searchQuery);
            setIsGettingLocation(false);
          }
        } catch (error) {
          console.error('Geocoding error:', error);
          setIsGettingLocation(false);
        }
      };
      
      geocodeSearch();
    } else if (!searchQuery) {
      getCurrentLocation();
    }
  }, [searchQuery]);
  
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log('Got user location:', latitude, longitude);
          setLat(latitude);
          setLng(longitude);
          setIsGettingLocation(false);
          
          // Update map center if map is already initialized
          if (map.current) {
            map.current.setCenter([longitude, latitude]);
          }
        },
        (error) => {
          console.warn('Geolocation failed, using default location:', error.message);
          setIsGettingLocation(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    } else {
      console.warn('Geolocation not supported, using default location');
      setIsGettingLocation(false);
    }
  };

  useEffect(() => {
    if (map.current || isGettingLocation) return; // initialize map only once and after getting location
    
    if (!mapboxgl.accessToken) {
      console.error('Mapbox access token is required');
      return;
    }
    
    try {
      console.log('Initializing map with token:', mapboxgl.accessToken);
      console.log('Map center:', [lng, lat]);
      
      if (!mapContainer.current) {
        console.error('Map container not available');
        return;
      }
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: mapStyle,
        center: [lng, lat],
        zoom: zoom,
        attributionControl: false, // We'll add custom attribution
        maxBounds: [[-180, 15], [-50, 72]] // Restrict map to United States bounds
      });

      map.current.addControl(new mapboxgl.AttributionControl({
        compact: true
      }), 'bottom-right');

      // Add navigation controls with custom styling
      const nav = new mapboxgl.NavigationControl({
        visualizePitch: true
      });
      map.current.addControl(nav, 'top-right');

      // Add search box with enhanced styling
      const geocoder = new MapboxGeocoder({
        accessToken: mapboxgl.accessToken,
        mapboxgl: mapboxgl,
        proximity: {
          longitude: lng,
          latitude: lat
        },
        placeholder: 'Search for properties...',
        marker: {
          color: '#667eea'
        },
        bbox: [-180, 15, -50, 72] // Bounding box for United States
      });
      map.current.addControl(geocoder, 'top-left');

      // Update state when map moves
      map.current.on('move', () => {
        setLng(map.current.getCenter().lng.toFixed(4));
        setLat(map.current.getCenter().lat.toFixed(4));
        setZoom(map.current.getZoom().toFixed(2));
      });

      map.current.on('load', () => {
        setIsMapReady(true);
      });
      
    } catch (error) {
      console.error('Error initializing Mapbox:', error);
    }
    
    // Clean up on unmount
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [isGettingLocation]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (map.current && mapStyle) {
      map.current.setStyle(mapStyle);
    }
  }, [mapStyle]);

  useEffect(() => {
    const loadProperties = async () => {
      try {
        setLoading(true);
        setError(null);

        const hasFilters = filters.priceRange.min > 0 ||
                          filters.priceRange.max < 2000000 ||
                          filters.bedrooms ||
                          filters.bathrooms ||
                          filters.propertyTypes.length > 0 ||
                          filters.sqftRange.min > 0 ||
                          filters.sqftRange.max < 5000;

        const propertiesData = hasFilters
          ? await fetchPropertiesWithFilters(filters)
          : await fetchProperties();

        if (searchCenter) {
          const filteredByLocation = filterPropertiesByRadius(
            propertiesData,
            searchCenter.lat,
            searchCenter.lng,
            10 // 10-mile radius
          );
          setProperties(filteredByLocation);
        } else {
          setProperties(propertiesData);
        }

        console.log(`Loaded ${propertiesData.length} properties`);
      } catch (err) {
        setError(err.message);
        console.error('Failed to load properties:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProperties();
  }, [filters, searchCenter]);

  useEffect(() => {
    console.log('Pin rendering useEffect triggered:', {
      hasMap: !!map.current,
      propertiesLength: properties.length,
      isMapReady,
      properties: properties.map(p => ({ id: p.id, lat: p.latitude, lng: p.longitude, address: p.address }))
    });

    if (!map.current || !properties.length || !isMapReady) {
      console.log('Pin rendering skipped - conditions not met');
      return;
    }

    console.log('Removing existing markers...');
    const existingMarkers = document.querySelectorAll('.mapboxgl-marker');
    console.log(`Found ${existingMarkers.length} existing markers to remove`);
    existingMarkers.forEach(marker => marker.remove());

    console.log(`Adding ${properties.length} new markers...`);
    properties.forEach((property, index) => {
      if (property.latitude && property.longitude) {
        console.log(`Creating marker ${index + 1} for property:`, {
          id: property.id,
          address: property.address,
          lat: property.latitude,
          lng: property.longitude,
          price: property.price
        });

        const markerEl = document.createElement('div');
        markerEl.className = 'property-marker';
        markerEl.innerHTML = `
          <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 10px 15px -3px rgba(102, 126, 234, 0.4), 0 4px 6px -2px rgba(102, 126, 234, 0.1);
            cursor: pointer;
            min-width: 70px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
          ">
            $${(property.price / 1000).toFixed(0)}k
          </div>
        `;

        const popup = new mapboxgl.Popup({
          offset: 25,
          closeButton: true,
          closeOnClick: false
        }).setHTML(`
          <div style="padding: 10px; min-width: 200px;">
            <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">
              ${property.address || property.title}
            </h3>
            <div style="margin-bottom: 8px;">
              <strong style="font-size: 16px; color: #2563eb;">
                $${property.price.toLocaleString()}
              </strong>
            </div>
            <div style="display: flex; gap: 12px; margin-bottom: 8px; font-size: 12px; color: #666;">
              <span>${property.bedrooms} beds</span>
              <span>${property.bathrooms} baths</span>
              <span>${property.squareFootage?.toLocaleString()} sq ft</span>
            </div>
            <div style="margin-bottom: 8px; font-size: 12px; color: #666;">
              ${property.propertyType}
            </div>
            <a href="/property/${property.id}" 
               style="
                 display: inline-block;
                 background: #2563eb;
                 color: white;
                 padding: 6px 12px;
                 text-decoration: none;
                 border-radius: 4px;
                 font-size: 12px;
                 font-weight: bold;
               ">
              View Details
            </a>
          </div>
        `);

        new mapboxgl.Marker(markerEl)
          .setLngLat([property.longitude, property.latitude])
          .setPopup(popup)
          .addTo(map.current);

        console.log(`Marker ${index + 1} added successfully:`, {
          id: property.id,
          coordinates: [property.longitude, property.latitude],
          markerElement: markerEl
        });
      } else {
        console.warn(`Property ${property.id} missing coordinates:`, {
          id: property.id,
          address: property.address,
          lat: property.latitude,
          lng: property.longitude
        });
      }
    });

    console.log('All markers processing complete');
  }, [properties, isMapReady]);

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const toggleFilters = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const toggleAssistant = () => {
    setIsAssistantOpen(!isAssistantOpen);
  };

  const changeMapStyle = (newStyle) => {
    setMapStyle(newStyle);
    if (map.current) {
      map.current.setStyle(newStyle);
    }
  };

  const mapStyles = [
    { id: 'streets', name: 'Streets', style: 'mapbox://styles/mapbox/streets-v12' },
    { id: 'light', name: 'Light', style: 'mapbox://styles/mapbox/light-v11' },
    { id: 'satellite', name: 'Satellite', style: 'mapbox://styles/mapbox/satellite-v9' },
    { id: 'satellite-streets', name: 'Satellite Streets', style: 'mapbox://styles/mapbox/satellite-streets-v12' },
    { id: 'dark', name: 'Dark', style: 'mapbox://styles/mapbox/dark-v11' },
    { id: 'outdoors', name: 'Outdoors', style: 'mapbox://styles/mapbox/outdoors-v12' }
  ];

  return (
    <div className={styles.propertiesPage}>
      <Header />
      <div className={styles.container}>
        <div className={styles.mapInfo}>
          <h2>
            {searchQuery ? `Properties near "${searchQuery}"` : 'Properties Near You'}
          </h2>
          {isGettingLocation && (
            <p style={{ fontSize: '14px', color: '#666', margin: '8px 0 0 0' }}>
              {searchQuery ? 'Finding location...' : 'Getting your location...'}
            </p>
          )}
        </div>
        
        <div className={styles.toolbar}>
          <div className={styles.toolbarLeft}>
            <button className={styles.filterToggle} onClick={toggleFilters}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>
              </svg>
              Filters
            </button>
            <div className={styles.mapStyleSelector}>
              <select 
                value={mapStyle} 
                onChange={(e) => changeMapStyle(e.target.value)}
                className={styles.mapStyleSelect}
              >
                {mapStyles.map(style => (
                  <option key={style.id} value={style.style}>
                    {style.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className={styles.toolbarRight}>
            <div className={styles.resultsCount}>
              {properties.length} properties found
            </div>
          </div>
        </div>
        
        <div className={styles.mapAndListings}>
          <FilterSidebar 
            filters={filters}
            onFiltersChange={handleFiltersChange}
            isOpen={isFilterOpen}
            onToggle={toggleFilters}
          />
          
          <div className={styles.contentWrapper}>
            <div ref={mapContainer} className={styles.map} />
            <div className={styles.propertiesSection}>
              <h3 className={styles.propertiesTitle}>Available Properties</h3>
              <div className={styles.propertiesGrid}>
                {loading ? (
                  <div className={styles.loadingMessage}>Loading properties...</div>
                ) : error ? (
                  <div className={styles.errorMessage}>Error: {error}</div>
                ) : properties.length === 0 ? (
                  <div className={styles.noPropertiesMessage}>No properties found.</div>
                ) : (
                  properties.map(property => (
                    isAuthenticated() ? (
                      <PropertyCardWithSave key={property.id} property={property} />
                    ) : (
                      <PropertyCard key={property.id} property={property} />
                    )
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        <RealEstateAssistant 
          isOpen={isAssistantOpen}
          onToggle={toggleAssistant}
          properties={properties}
        />
      </div>
    </div>
  );
}

export default Properties;
