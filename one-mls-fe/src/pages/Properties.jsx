import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import mapboxgl from 'mapbox-gl';
import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder';
import Header from '../components/Header';
import PropertyCard from '../components/PropertyCard';
import PropertyCardWithSave from '../components/PropertyCardWithSave';
import FilterSidebar from '../components/FilterSidebar';
import RealEstateAssistant from '../components/RealEstateAssistant';
import { fetchProperties, fetchPropertiesWithFilters } from '../utils/api';
import { isAuthenticated } from '../utils/auth';
import { filterPropertiesByRadius } from '../utils/distance';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css';
import styles from './Properties.module.css';

// You'll need to get a Mapbox access token from https://account.mapbox.com/
// and replace this placeholder with your actual token
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoicGluZ2hzdSIsImEiOiJjbHVlZWlmYzQwMDk1MnFuenE4dG1nOGE3In0.4Zdx7JcZWlTlK-O-kBbpNQ';
console.log("mapbox access token", mapboxgl.accessToken);

if (!mapboxgl.accessToken) {
  console.error('Mapbox access token is not set. Please check your .env file.');
}

function Properties() {
  const [searchParams] = useSearchParams();
  const searchQuery = searchParams.get('search');
  
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [lng, setLng] = useState(-121.9885); // Default: Fremont, CA longitude
  const [lat, setLat] = useState(37.5485);   // Default: Fremont, CA latitude
  const [zoom, setZoom] = useState(12);
  const [isGettingLocation, setIsGettingLocation] = useState(true);
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchCenter, setSearchCenter] = useState(null); // Store search location center
  const [filters, setFilters] = useState({
    priceRange: { min: 0, max: 2000000 },
    bedrooms: '',
    bathrooms: '',
    propertyTypes: [],
    sqftRange: { min: 0, max: 5000 }
  });

  const [isAssistantOpen, setIsAssistantOpen] = useState(false);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    if (searchQuery && mapboxgl.accessToken) {
      const geocodeSearch = async () => {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchQuery)}.json?access_token=${mapboxgl.accessToken}&country=US&limit=1`
          );
          const data = await response.json();
          
          if (data.features && data.features.length > 0) {
            const [longitude, latitude] = data.features[0].center;
            setLng(longitude);
            setLat(latitude);
            setSearchCenter({ lat: latitude, lng: longitude });
            setIsGettingLocation(false);
            
            // Update map center if map is already initialized
            if (map.current) {
              map.current.setCenter([longitude, latitude]);
              map.current.setZoom(12);
            }
          } else {
            console.warn('No results found for search query:', searchQuery);
            setIsGettingLocation(false);
          }
        } catch (error) {
          console.error('Geocoding error:', error);
          setIsGettingLocation(false);
        }
      };
      
      geocodeSearch();
    } else if (!searchQuery) {
      getCurrentLocation();
    }
  }, [searchQuery]);
  
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log('Got user location:', latitude, longitude);
          setLat(latitude);
          setLng(longitude);
          setIsGettingLocation(false);
          
          // Update map center if map is already initialized
          if (map.current) {
            map.current.setCenter([longitude, latitude]);
          }
        },
        (error) => {
          console.warn('Geolocation failed, using default location:', error.message);
          setIsGettingLocation(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    } else {
      console.warn('Geolocation not supported, using default location');
      setIsGettingLocation(false);
    }
  };

  useEffect(() => {
    if (map.current) return; // initialize map only once
    
    try {
      console.log('Initializing map with token:', mapboxgl.accessToken);
      console.log('Map center:', [lng, lat]);
      
      if (!mapContainer.current) {
        console.error('Map container not available');
        return;
      }
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: mapStyle,
        center: [lng, lat],
        zoom: zoom,
        attributionControl: false, // We'll add custom attribution
        maxBounds: [[-180, 15], [-50, 72]] // Restrict map to United States bounds
      });

      map.current.addControl(new mapboxgl.AttributionControl({
        compact: true
      }), 'bottom-right');

      // Add navigation controls with custom styling
      const nav = new mapboxgl.NavigationControl({
        visualizePitch: true
      });
      map.current.addControl(nav, 'top-right');

      // Add search box with enhanced styling
      const geocoder = new MapboxGeocoder({
        accessToken: mapboxgl.accessToken,
        mapboxgl: mapboxgl,
        proximity: {
          longitude: lng,
          latitude: lat
        },
        placeholder: 'Search for properties...',
        marker: {
          color: '#667eea'
        },
        bbox: [-180, 15, -50, 72] // Bounding box for United States
      });
      map.current.addControl(geocoder, 'top-left');

      // Update state when map moves
      map.current.on('move', () => {
        setLng(map.current.getCenter().lng.toFixed(4));
        setLat(map.current.getCenter().lat.toFixed(4));
        setZoom(map.current.getZoom().toFixed(2));
      });

      map.current.on('load', () => {
        setIsMapReady(true);
        console.log("Map is ready!");
      });
      
    } catch (error) {
      console.error('Error initializing Mapbox:', error);
    }
    
    // Clean up on unmount
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [mapStyle, isGettingLocation]); // Removed lng, lat, zoom to prevent re-initialization on map movement

  useEffect(() => {
    if (map.current && mapStyle) {
      map.current.setStyle(mapStyle);
    }
  }, [mapStyle]);

  useEffect(() => {
    const loadProperties = async () => {
      try {
        setLoading(true);
        setError(null);

        const hasFilters = filters.priceRange.min > 0 ||
                          filters.priceRange.max < 2000000 ||
                          filters.bedrooms ||
                          filters.bathrooms ||
                          filters.propertyTypes.length > 0 ||
                          filters.sqftRange.min > 0 ||
                          filters.sqftRange.max < 5000;

        const propertiesData = hasFilters
          ? await fetchPropertiesWithFilters(filters)
          : await fetchProperties();

        if (searchCenter) {
          const filteredByLocation = filterPropertiesByRadius(
            propertiesData,
            searchCenter.lat,
            searchCenter.lng,
            10 // 10-mile radius
          );
          setProperties(filteredByLocation);
        } else {
          setProperties(propertiesData);
        }

        console.log(`Loaded ${propertiesData.length} properties`);
      } catch (err) {
        setError(err.message);
        console.error('Failed to load properties:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProperties();
  }, [filters, searchCenter]);

  useEffect(() => {
    console.log('Pin rendering useEffect triggered:', {
      hasMap: !!map.current,
      propertiesLength: properties.length,
      isMapReady,
      properties: properties.map(p => ({ id: p.id, lat: p.latitude, lng: p.longitude, address: p.address }))
    });

    if (!map.current || !properties.length || !isMapReady) {
      console.log('Pin rendering skipped - conditions not met');
      return;
    }

    console.log('Removing existing markers...');
    const existingMarkers = document.querySelectorAll('.mapboxgl-marker');
    console.log(`Found ${existingMarkers.length} existing markers to remove`);
    existingMarkers.forEach(marker => marker.remove());

    console.log(`Adding ${properties.length} new markers...`);
    properties.forEach((property, index) => {
      if (property.latitude && property.longitude) {
        console.log(`Creating marker ${index + 1} for property:`, {
          id: property.id,
          address: property.address,
          lat: property.latitude,
          lng: property.longitude,
          price: property.price
        });

        const markerEl = document.createElement('div');
        markerEl.className = 'property-marker';
        
        // Get the first image or a placeholder if no images
        const previewImage = property.images && property.images.length > 0 
          ? property.images[0] 
          : 'https://placehold.co/200x150/667eea/ffffff?text=No+Image';
        
        markerEl.innerHTML = `
          <div class="marker-container">
            <div class="marker-price">
              $${(property.price / 1000).toFixed(0)}k
            </div>
            <div class="marker-preview">
              <div class="preview-image" style="background-image: url('${previewImage}')"></div>
              <div class="preview-details">
                <div class="preview-address">${property.address || 'Property for Sale'}</div>
                <div class="preview-specs">
                  <span>${property.bedrooms || 'N/A'} bd</span>
                  <span>${property.bathrooms || 'N/A'} ba</span>
                  <span>${property.squareFootage ? property.squareFootage.toLocaleString() + ' sqft' : ''}</span>
                </div>
                <div class="preview-price">$${property.price.toLocaleString()}</div>
              </div>
            </div>
          </div>
          <style>
            .marker-container {
              position: relative;
              z-index: 1;
            }
            .marker-price {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 8px 12px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: bold;
              box-shadow: 0 10px 15px -3px rgba(102, 126, 234, 0.4), 0 4px 6px -2px rgba(102, 126, 234, 0.1);
              cursor: pointer;
              min-width: 70px;
              text-align: center;
              border: 2px solid rgba(255, 255, 255, 0.2);
              backdrop-filter: blur(10px);
              transition: all 0.3s ease;
              position: relative;
              z-index: 2;
            }
            .marker-preview {
              position: absolute;
              bottom: 100%;
              left: 50%;
              transform: translateX(-50%) translateY(10px) scale(0.95);
              width: 250px;
              background: white;
              border-radius: 12px;
              box-shadow: 0 10px 25px rgba(0,0,0,0.15);
              opacity: 0;
              visibility: hidden;
              transition: all 0.3s ease;
              z-index: 1;
              overflow: hidden;
              pointer-events: none;
            }
            .marker-container:hover .marker-preview {
              opacity: 1;
              visibility: visible;
              transform: translateX(-50%) translateY(-10px) scale(1);
            }
            .preview-image {
              height: 120px;
              background-size: cover;
              background-position: center;
              border-top-left-radius: 12px;
              border-top-right-radius: 12px;
            }
            .preview-details {
              padding: 12px;
            }
            .preview-address {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 6px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .preview-specs {
              display: flex;
              gap: 8px;
              font-size: 12px;
              color: #666;
              margin-bottom: 6px;
            }
            .preview-price {
              font-weight: bold;
              color: #2563eb;
              font-size: 15px;
            }
          </style>
        `;

        const popup = new mapboxgl.Popup({
          offset: 25,
          closeButton: true,
          closeOnClick: false
        }).setHTML(`
          <div style="padding: 10px; min-width: 200px;">
            <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">
              ${property.address || property.title}
            </h3>
            <div style="margin-bottom: 8px;">
              <strong style="font-size: 16px; color: #2563eb;">
                $${property.price.toLocaleString()}
              </strong>
            </div>
            <div style="display: flex; gap: 12px; margin-bottom: 8px; font-size: 12px; color: #666;">
              <span>${property.bedrooms} beds</span>
              <span>${property.bathrooms} baths</span>
              <span>${property.squareFootage?.toLocaleString()} sq ft</span>
            </div>
            <div style="margin-bottom: 8px; font-size: 12px; color: #666;">
              ${property.propertyType}
            </div>
            <a href="/property/${property.id}" 
               style="
                 display: inline-block;
                 background: #2563eb;
                 color: white;
                 padding: 6px 12px;
                 text-decoration: none;
                 border-radius: 4px;
                 font-size: 12px;
                 font-weight: bold;
               ">
              View Details
            </a>
          </div>
        `);

        new mapboxgl.Marker(markerEl)
          .setLngLat([property.longitude, property.latitude])
          .setPopup(popup)
          .addTo(map.current);

        console.log(`Marker ${index + 1} added successfully:`, {
          id: property.id,
          coordinates: [property.longitude, property.latitude],
          markerElement: markerEl
        });
      } else {
        console.warn(`Property ${property.id} missing coordinates:`, {
          id: property.id,
          address: property.address,
          lat: property.latitude,
          lng: property.longitude
        });
      }
    });

    console.log('All markers processing complete');
  }, [properties, isMapReady]);

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const toggleAssistant = () => {
    setIsAssistantOpen(!isAssistantOpen);
  };

  const changeMapStyle = (newStyle) => {
    setMapStyle(newStyle);
    if (map.current) {
      map.current.setStyle(newStyle);
    }
  };

  const mapStyles = [
    { id: 'streets', name: 'Streets', style: 'mapbox://styles/mapbox/streets-v12' },
    { id: 'light', name: 'Light', style: 'mapbox://styles/mapbox/light-v11' },
    { id: 'satellite', name: 'Satellite', style: 'mapbox://styles/mapbox/satellite-v9' },
    { id: 'satellite-streets', name: 'Satellite Streets', style: 'mapbox://styles/mapbox/satellite-streets-v12' },
    { id: 'dark', name: 'Dark', style: 'mapbox://styles/mapbox/dark-v11' },
    { id: 'outdoors', name: 'Outdoors', style: 'mapbox://styles/mapbox/outdoors-v12' }
  ];

  return (
    <div className={styles.propertiesPage}>
      <div className={styles.headerOverlay}>
        <Header />
      </div>
      <div className={styles.filtersOverlay}>
        <FilterSidebar
          filters={filters}
          onFiltersChange={handleFiltersChange}
          isOpen={true}
          onToggle={() => {}}
          isHorizontal={true}
        />
      </div>

      <div className={styles.contentContainer}>
        <div className={styles.propertiesColumn}>
          <h2 className={styles.propertiesTitle}>Available Properties</h2>
          <div className={styles.propertiesGrid}>
            {loading ? (
              <div className={styles.loadingMessage}>Loading properties...</div>
            ) : error ? (
              <div className={styles.errorMessage}>Error: {error}</div>
            ) : properties.length === 0 ? (
              <div className={styles.noPropertiesMessage}>No properties found.</div>
            ) : (
              properties.map(property => (
                isAuthenticated() ? (
                  <PropertyCardWithSave key={property.id} property={property} />
                ) : (
                  <PropertyCard key={property.id} property={property} />
                )
              ))
            )}
          </div>
        </div>

        <div className={styles.mapColumn}>
          <div className={styles.mapControls}>
            <select
              value={mapStyle}
              onChange={(e) => changeMapStyle(e.target.value)}
              className={styles.mapStyleSelect}
            >
              {mapStyles.map(style => (
                <option key={style.id} value={style.style}>
                  {style.name}
                </option>
              ))}
            </select>
          </div>
          <div ref={mapContainer} className={styles.map} />
        </div>
      </div>

      <RealEstateAssistant
        isOpen={isAssistantOpen}
        onToggle={toggleAssistant}
        properties={properties}
      />
    </div>
  );
}

export default Properties;
