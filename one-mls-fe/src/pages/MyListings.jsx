import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import mapboxgl from 'mapbox-gl';
import Header from '../components/Header';
import MessagingPortal from '../components/MessagingPortal';
import { getAuthHeaders } from '../utils/auth';
import 'mapbox-gl/dist/mapbox-gl.css';
import styles from './MyListings.module.css';

mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;


function MyListings() {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [lng, setLng] = useState(-121.9885); // Default: Fremont, CA longitude
  const [lat, setLat] = useState(37.5485);   // Default: Fremont, CA latitude
  const [zoom, setZoom] = useState(12);
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedListingId, setSelectedListingId] = useState(null);
  const [showMessagingPortal, setShowMessagingPortal] = useState(false);
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    fetchUserListings();
  }, []);

  const fetchUserListings = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BACKEND_ENDPOINT}/api/user/listings`, {
        headers: getAuthHeaders()
      });

      if (response.ok) {
        const data = await response.json();
        setListings(data.listings || []);
      } else {
        setError('Failed to load your listings');
      }
    } catch (err) {
      setError('Error loading listings');
      console.error('Error fetching user listings:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (map.current || !mapContainer.current) return;
    
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [lng, lat],
      zoom: zoom,
      attributionControl: false
    });

    map.current.on('load', () => {
      setIsMapReady(true);
    });

    map.current.on('move', () => {
      setLng(map.current.getCenter().lng.toFixed(4));
      setLat(map.current.getCenter().lat.toFixed(4));
      setZoom(map.current.getZoom().toFixed(2));
    });

    return () => map.current?.remove();
  }, []); // Removed lng, lat, zoom to prevent re-initialization on map movement

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const handleViewMessages = (listingId) => {
    setSelectedListingId(listingId);
    setShowMessagingPortal(true);
  };

  const handleCloseMessaging = () => {
    setShowMessagingPortal(false);
    setSelectedListingId(null);
  };

  useEffect(() => {
    if (!isMapReady || !listings.length) return;

    listings.forEach((listing) => {
      const markerEl = document.createElement('div');
      markerEl.style.cssText = `
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
        border: 2px solid white;
        border-radius: 20px;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        position: relative;
        transform: translate(-50%, -100%);
        white-space: nowrap;
      `;
      markerEl.textContent = formatPrice(listing.price);

      const popupContent = `
        <div style="padding: 12px; min-width: 250px;">
          <div style="margin-bottom: 8px;">
            <img src="${listing.images[0]}" alt="${listing.address}" 
                 style="width: 100%; height: 120px; object-fit: cover; border-radius: 6px;" />
          </div>
          <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #1f2937;">
            ${listing.address}
          </h3>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 18px; font-weight: 700; color: #059669;">
              ${formatPrice(listing.price)}
            </span>
          </div>
          <div style="display: flex; gap: 12px; margin-bottom: 12px; font-size: 14px; color: #6b7280;">
            <span>${listing.bedrooms} bed</span>
            <span>${listing.bathrooms} bath</span>
            <span>${listing.squareFootage} sqft</span>
          </div>
          <div style="margin-bottom: 12px;">
            <span style="font-size: 14px; color: #374151; background: #f3f4f6; padding: 2px 6px; border-radius: 4px;">
              ${listing.propertyType}
            </span>
          </div>
          <div style="display: flex; gap: 8px;">
            <button onclick="window.location.href='/listing/${listing.id}'" 
                    style="flex: 1; background: #3b82f6; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;">
              View Details
            </button>
            <button onclick="handleMessagesFromPopup(${listing.id})" 
                    style="flex: 1; background: #10b981; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;">
              Messages
            </button>
          </div>
        </div>
      `;

      const popup = new mapboxgl.Popup({
        offset: 25,
        closeButton: true,
        closeOnClick: false,
        maxWidth: '300px'
      }).setHTML(popupContent);

      new mapboxgl.Marker(markerEl)
        .setLngLat([listing.longitude, listing.latitude])
        .setPopup(popup)
        .addTo(map.current);
    });

    window.handleMessagesFromPopup = (listingId) => {
      handleViewMessages(listingId);
    };

  }, [isMapReady, listings, formatPrice, handleViewMessages]);

  const handleDeleteListing = async (listingId) => {
    if (!window.confirm('Are you sure you want to delete this listing?')) {
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BACKEND_ENDPOINT}/api/listings/${listingId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (response.ok) {
        setListings(listings.filter(listing => listing.id !== listingId));
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to delete listing');
      }
    } catch (err) {
      setError('Error deleting listing');
      console.error('Error deleting listing:', err);
    }
  };

  if (loading) {
    return (
      <div className="app">
        <Header />
        <main className="main-content">
          <div className={styles.loadingContainer}>
            <div className={styles.loading}>Loading your listings...</div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header />
      
      <main className="main-content">
        <div className={styles.listingsContainer}>
          <div className={styles.header}>
            <h1>My Listings</h1>
            <Link to="/list-property" className={styles.addButton}>
              + Add New Listing
            </Link>
          </div>

          {error && <div className="error-message">{error}</div>}

          {listings.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>
                <img 
                  src="https://undraw.co/api/illustrations/starry-window" 
                  alt="No listings" 
                  className={styles.emptyImage}
                />
              </div>
              <h3>No listings yet</h3>
              <p>You haven't created any property listings yet. Start by adding your first property!</p>
              <Link to="/list-property" className={styles.ctaButton}>
                Create Your First Listing
              </Link>
            </div>
          ) : (
            <div className={styles.listingsLayout}>
              <div className={styles.mapContainer}>
                <div ref={mapContainer} className={styles.map} />
              </div>
              <div className={styles.listingsList}>
                {listings.map((listing) => (
                  <div key={listing.id} className={styles.listingItem}>
                    <div className={styles.listingImage}>
                      {listing.images && listing.images.length > 0 ? (
                        <img 
                          src={listing.images[0]} 
                          alt={listing.address}
                        />
                      ) : (
                        <div className={styles.noImage}>
                          <span>📷</span>
                        </div>
                      )}
                    </div>
                    
                    <div className={styles.listingInfo}>
                      <h3>{listing.address}</h3>
                      <div className={styles.price}>{formatPrice(listing.price)}</div>
                      <div className={styles.specs}>
                        {listing.bedrooms} bed • {listing.bathrooms} bath • {listing.squareFootage} sqft
                      </div>
                      <div className={styles.propertyType}>{listing.propertyType}</div>
                    </div>
                    
                    <div className={styles.listingActions}>
                      <Link to={`/listing/${listing.id}`} className={styles.viewButton}>
                        View
                      </Link>
                      <button onClick={() => handleViewMessages(listing.id)} className={styles.messagesButton}>
                        Messages
                      </button>
                      <button onClick={() => handleDeleteListing(listing.id)} className={styles.deleteButton}>
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
      
      <footer className="footer">
        <p>&copy; {new Date().getFullYear()} OneMLS. All rights reserved.</p>
      </footer>

      {showMessagingPortal && (
        <MessagingPortal 
          listingId={selectedListingId}
          onClose={handleCloseMessaging}
        />
      )}
    </div>
  );
}

export default MyListings;
